'use client';

import { useState, useCallback, useEffect } from 'react';

import {
  <PERSON>,
  <PERSON>,
  Stack,
  Button,
  Typography,
  Container,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControlLabel,
  Switch
} from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

import { useCustomWorkflow } from 'src/actions/mooly-chatbot/use-custom-workflow';
import { validateWorkflowConfiguration } from 'src/actions/mooly-chatbot/workflow-validation-service';
import { PRODUCT_TYPE_LABELS } from 'src/actions/mooly-chatbot/order-status-business-rules';

import WorkflowCanvas from './workflow-canvas';
import WorkflowTemplateDialog from './workflow-template-dialog';
import WorkflowStatusIndicator, { WorkflowValidationSummary } from './workflow-status-indicator';
import WorkflowInsights from './workflow-insights';

// ----------------------------------------------------------------------

export default function WorkflowBuilderView() {
  const [selectedProductType, setSelectedProductType] = useState('simple');
  const [selectedWorkflow, setSelectedWorkflow] = useState(null);
  
  // Dialogs
  const createDialog = useBoolean();
  const templateDialog = useBoolean();
  
  // Workflow hook
  const {
    workflows,
    templates,
    loading,
    createWorkflow,
    createFromTemplate,
    updateWorkflow,
    deleteWorkflow,
    activeWorkflows,
    workflowsByType
  } = useCustomWorkflow({ productType: selectedProductType });

  // Lấy workflows theo product type hiện tại
  const currentWorkflows = workflowsByType[selectedProductType] || [];

  const handleCreateWorkflow = useCallback(async (workflowData) => {
    // Validate workflow configuration trước khi tạo
    const validation = validateWorkflowConfiguration(
      { ...workflowData, product_type: selectedProductType },
      workflows
    );

    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errors.join('; '),
        validation
      };
    }

    const result = await createWorkflow({
      ...workflowData,
      product_type: selectedProductType
    });

    if (result.success) {
      createDialog.onFalse();
      setSelectedWorkflow(result.data);
    }

    return result;
  }, [createWorkflow, selectedProductType, createDialog, workflows]);

  const handleCreateFromTemplate = useCallback(async (templateType, customData) => {
    const result = await createFromTemplate(templateType, {
      ...customData,
      product_type: selectedProductType
    });
    
    if (result.success) {
      templateDialog.onFalse();
      setSelectedWorkflow(result.data);
    }
    
    return result;
  }, [createFromTemplate, selectedProductType, templateDialog]);

  const renderHeader = (
    <Container maxWidth="xl">
      <CustomBreadcrumbs
        heading="Quản lý quy trình đơn hàng"
        subHeading="Tạo và tùy chỉnh quy trình xử lý đơn hàng theo nhu cầu kinh doanh"
        links={[
          { name: 'Dashboard', href: '/' },
          { name: 'Đơn hàng', href: '/orders' },
          { name: 'Quy trình' }
        ]}
        action={
          <Stack direction="row" spacing={1}>
            <Button
              variant="outlined"
              startIcon={<Iconify icon="solar:document-add-bold" />}
              onClick={templateDialog.onTrue}
            >
              Từ mẫu có sẵn
            </Button>
            <Button
              variant="contained"
              startIcon={<Iconify icon="solar:add-circle-bold" />}
              onClick={createDialog.onTrue}
            >
              Tạo quy trình mới
            </Button>
          </Stack>
        }
      />
    </Container>
  );

  const renderProductTypeSelector = (
    <Container maxWidth="xl">
      <Card sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Chọn loại sản phẩm
        </Typography>
        <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
          {Object.entries(PRODUCT_TYPE_LABELS).map(([type, label]) => (
            type !== 'all' && (
              <Chip
                key={type}
                label={label}
                variant={selectedProductType === type ? 'filled' : 'outlined'}
                color={selectedProductType === type ? 'primary' : 'default'}
                onClick={() => setSelectedProductType(type)}
                sx={{ mb: 1 }}
              />
            )
          ))}
        </Stack>
      </Card>
    </Container>
  );

  const renderWorkflowList = (
    <Container maxWidth="xl">
      <Grid container spacing={3}>
        <Grid item size={{ xs: 12, md: 4 }}>
          <Card sx={{ p: 3, height: 'fit-content' }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Danh sách quy trình
              <Chip 
                label={currentWorkflows.length} 
                size="small" 
                sx={{ ml: 1 }} 
              />
            </Typography>
            
            <Stack spacing={2}>
              {currentWorkflows.map((workflow) => (
                <Card
                  key={workflow.id}
                  variant={selectedWorkflow?.id === workflow.id ? 'elevation' : 'outlined'}
                  sx={{
                    p: 2,
                    cursor: 'pointer',
                    border: selectedWorkflow?.id === workflow.id ? 2 : 1,
                    borderColor: selectedWorkflow?.id === workflow.id ? 'primary.main' : 'divider',
                    '&:hover': {
                      borderColor: 'primary.main',
                      bgcolor: 'action.hover'
                    }
                  }}
                  onClick={() => setSelectedWorkflow(workflow)}
                >
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography variant="subtitle2" noWrap>
                        {workflow.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" noWrap>
                        {workflow.stages?.length || 0} bước
                      </Typography>
                    </Box>
                    
                    <Stack direction="row" spacing={0.5}>
                      {workflow.is_default && (
                        <Chip label="Mặc định" size="small" color="success" />
                      )}
                      {!workflow.is_active && (
                        <Chip label="Tạm dừng" size="small" color="error" />
                      )}
                    </Stack>
                  </Stack>
                  
                  {workflow.description && (
                    <Typography 
                      variant="caption" 
                      color="text.secondary" 
                      sx={{ mt: 1, display: 'block' }}
                    >
                      {workflow.description}
                    </Typography>
                  )}
                </Card>
              ))}
              
              {currentWorkflows.length === 0 && (
                <Box textAlign="center" py={4}>
                  <Typography variant="body2" color="text.secondary">
                    Chưa có quy trình nào cho {PRODUCT_TYPE_LABELS[selectedProductType]}
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<Iconify icon="solar:add-circle-bold" />}
                    onClick={templateDialog.onTrue}
                    sx={{ mt: 2 }}
                  >
                    Tạo từ mẫu
                  </Button>
                </Box>
              )}
            </Stack>
          </Card>
        </Grid>
        
        <Grid item size={{xs: 12, md: 8 }}>
          <Card sx={{ p: 3, minHeight: 600 }}>
            {/* Loading State */}
            {loading && (
              <WorkflowStatusIndicator
                loading
                showProgress
              />
            )}

            {/* Selected Workflow */}
            {!loading && selectedWorkflow && (
              <WorkflowCanvas
                workflow={selectedWorkflow}
                onUpdate={(updatedWorkflow) => setSelectedWorkflow(updatedWorkflow)}
              />
            )}

            {/* Empty State */}
            {!loading && !selectedWorkflow && (
              <Box
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                height={400}
                color="text.secondary"
              >
                <Iconify icon="solar:document-text-bold" width={64} sx={{ mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Chọn quy trình để chỉnh sửa
                </Typography>
                <Typography variant="body2" textAlign="center">
                  Chọn một quy trình từ danh sách bên trái để xem và chỉnh sửa chi tiết
                </Typography>
              </Box>
            )}
          </Card>
        </Grid>
      </Grid>
    </Container>
  );

  return (
    <>
      <Box sx={{ py: 3 }}>
        {renderHeader}
        {renderProductTypeSelector}

        {/* Workflow Insights */}
        <Container maxWidth="xl" sx={{ mb: 3 }}>
          <WorkflowInsights
            workflows={workflows}
            selectedProductType={selectedProductType}
          />
        </Container>

        {renderWorkflowList}
      </Box>

      {/* Create Workflow Dialog */}
      <CreateWorkflowDialog
        open={createDialog.value}
        onClose={createDialog.onFalse}
        onSubmit={handleCreateWorkflow}
        productType={selectedProductType}
        loading={loading}
        workflows={workflows}
      />

      {/* Template Dialog */}
      <WorkflowTemplateDialog
        open={templateDialog.value}
        onClose={templateDialog.onFalse}
        onSubmit={handleCreateFromTemplate}
        templates={templates}
        productType={selectedProductType}
        loading={loading}
      />
    </>
  );
}

// ----------------------------------------------------------------------

function CreateWorkflowDialog({ open, onClose, onSubmit, productType, loading, workflows = [] }) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true,
    is_default: false
  });

  const [validationErrors, setValidationErrors] = useState({});
  const [suggestions, setSuggestions] = useState([]);
  const [isValidating, setIsValidating] = useState(false);

  // Auto-suggestions dựa trên product type
  const getWorkflowSuggestions = useCallback((currentProductType) => {
    const workflowSuggestions = {
      simple: [
        { name: 'Quy trình bán hàng chuẩn', description: 'Quy trình xử lý đơn hàng sản phẩm vật lý từ đặt hàng đến giao hàng' },
        { name: 'Quy trình bán hàng nhanh', description: 'Quy trình rút gọn cho sản phẩm có sẵn, giao hàng trong ngày' },
        { name: 'Quy trình pre-order', description: 'Quy trình cho sản phẩm đặt trước, có thời gian chờ sản xuất' }
      ],
      variable: [
        { name: 'Quy trình sản phẩm biến thể', description: 'Quy trình xử lý đơn hàng có nhiều lựa chọn màu sắc, kích thước' },
        { name: 'Quy trình combo sản phẩm', description: 'Quy trình cho đơn hàng gồm nhiều sản phẩm khác nhau' },
        { name: 'Quy trình tùy chỉnh', description: 'Quy trình cho sản phẩm có thể tùy chỉnh theo yêu cầu khách hàng' }
      ],
      digital: [
        { name: 'Quy trình sản phẩm số', description: 'Quy trình giao hàng tự động cho sản phẩm số như ebook, software' },
        { name: 'Quy trình khóa học online', description: 'Quy trình cấp quyền truy cập khóa học sau thanh toán' },
        { name: 'Quy trình license phần mềm', description: 'Quy trình cấp license key và hướng dẫn cài đặt' }
      ],
      service: [
        { name: 'Quy trình đặt lịch dịch vụ', description: 'Quy trình từ đặt lịch đến hoàn thành dịch vụ' },
        { name: 'Quy trình tư vấn online', description: 'Quy trình tư vấn qua video call hoặc chat' },
        { name: 'Quy trình dịch vụ tại nhà', description: 'Quy trình cho dịch vụ cần đến tận nơi khách hàng' }
      ]
    };
    return workflowSuggestions[currentProductType] || workflowSuggestions.simple;
  }, []);

  // Validate tên workflow (check duplicate và format)
  const validateWorkflowName = useCallback(async (name) => {
    if (!name.trim()) {
      return { isValid: false, message: 'Tên quy trình không được để trống' };
    }

    if (name.length < 3) {
      return { isValid: false, message: 'Tên quy trình phải có ít nhất 3 ký tự' };
    }

    if (name.length > 100) {
      return { isValid: false, message: 'Tên quy trình không được quá 100 ký tự' };
    }

    // Check duplicate trong cùng product type
    const existingWorkflow = workflows.find(w =>
      w.product_type === productType &&
      w.name.toLowerCase().trim() === name.toLowerCase().trim()
    );

    if (existingWorkflow) {
      return { isValid: false, message: 'Tên quy trình đã tồn tại cho loại sản phẩm này' };
    }

    return { isValid: true, message: '' };
  }, [workflows, productType]);

  // Handle name change với debounced validation
  const handleNameChange = useCallback(async (event) => {
    const name = event.target.value;
    setFormData(prev => ({ ...prev, name }));

    // Clear previous validation
    setValidationErrors(prev => ({ ...prev, name: '' }));

    if (name.trim()) {
      setIsValidating(true);

      // Debounce validation
      setTimeout(async () => {
        const validation = await validateWorkflowName(name);
        setValidationErrors(prev => ({
          ...prev,
          name: validation.isValid ? '' : validation.message
        }));
        setIsValidating(false);
      }, 500);
    }
  }, [validateWorkflowName]);

  // Apply suggestion
  const applySuggestion = useCallback((suggestion) => {
    setFormData(prev => ({
      ...prev,
      name: suggestion.name,
      description: suggestion.description
    }));
    setValidationErrors(prev => ({ ...prev, name: '' }));
  }, []);

  // Reset form khi đóng dialog
  useEffect(() => {
    if (!open) {
      setFormData({ name: '', description: '', is_active: true, is_default: false });
      setValidationErrors({});
      setSuggestions([]);
    } else {
      // Load suggestions khi mở dialog
      setSuggestions(getWorkflowSuggestions(productType));
    }
  }, [open, productType, getWorkflowSuggestions]);

  const handleSubmit = useCallback(async () => {
    // Final validation
    const nameValidation = await validateWorkflowName(formData.name);
    if (!nameValidation.isValid) {
      setValidationErrors(prev => ({ ...prev, name: nameValidation.message }));
      return;
    }

    const result = await onSubmit(formData);
    if (result.success) {
      setFormData({ name: '', description: '', is_active: true, is_default: false });
      setValidationErrors({});
    }
  }, [formData, onSubmit, validateWorkflowName]);

  const handleChange = useCallback((field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  const isFormValid = formData.name.trim() && !validationErrors.name && !isValidating;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={1}>
          <Iconify icon="solar:add-circle-bold" />
          <Typography variant="h6">
            Tạo quy trình mới cho {PRODUCT_TYPE_LABELS[productType]}
          </Typography>
        </Stack>
      </DialogTitle>

      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {/* Suggestions */}
          {suggestions.length > 0 && (
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Gợi ý quy trình phổ biến:
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
                {suggestions.map((suggestion, index) => (
                  <Chip
                    key={index}
                    label={suggestion.name}
                    variant="outlined"
                    size="small"
                    onClick={() => applySuggestion(suggestion)}
                    sx={{ mb: 1 }}
                  />
                ))}
              </Stack>
            </Box>
          )}

          {/* Workflow Name */}
          <TextField
            label="Tên quy trình"
            value={formData.name}
            onChange={handleNameChange}
            fullWidth
            required
            error={!!validationErrors.name}
            helperText={validationErrors.name || 'Tên quy trình phải duy nhất trong cùng loại sản phẩm'}
            placeholder="VD: Quy trình bán hàng chuẩn"
            slotProps={{
              input: {
                endAdornment: isValidating && (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Iconify icon="solar:refresh-bold" sx={{ animation: 'spin 1s linear infinite' }} />
                  </Box>
                )
              }
            }}
          />

          {/* Description */}
          <TextField
            label="Mô tả quy trình"
            value={formData.description}
            onChange={handleChange('description')}
            fullWidth
            multiline
            rows={3}
            placeholder="Mô tả chi tiết về quy trình này, các bước xử lý và mục đích sử dụng..."
            helperText="Mô tả giúp team hiểu rõ mục đích và cách sử dụng quy trình"
          />

          {/* Settings */}
          <Stack spacing={2}>
            <FormControlLabel
              control={
                <Switch
                  checked={formData.is_active}
                  onChange={handleChange('is_active')}
                />
              }
              label="Kích hoạt ngay sau khi tạo"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={formData.is_default}
                  onChange={handleChange('is_default')}
                />
              }
              label="Đặt làm quy trình mặc định cho loại sản phẩm này"
            />
          </Stack>
        </Stack>
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 3 }}>
        <Button onClick={onClose} size="large">
          Hủy
        </Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={!isFormValid || loading}
          size="large"
          startIcon={loading ? <Iconify icon="solar:refresh-bold" sx={{ animation: 'spin 1s linear infinite' }} /> : <Iconify icon="solar:add-circle-bold" />}
        >
          {loading ? 'Đang tạo...' : 'Tạo quy trình'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
