'use client';

import { useState, useCallback } from 'react';

import {
  <PERSON>,
  <PERSON>,
  Stack,
  Button,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  Grid,
  IconButton,
  Tooltip,
  Divider
} from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { Label } from 'src/components/label';

import { useCustomWorkflow } from 'src/actions/mooly-chatbot/use-custom-workflow';
import { PRODUCT_TYPE_LABELS } from 'src/actions/mooly-chatbot/order-status-business-rules';

// ----------------------------------------------------------------------

export default function WorkflowSettingsDialog({ open, onClose }) {
  const [selectedProductType, setSelectedProductType] = useState('simple');
  
  // Workflow hook
  const {
    workflows,
    loading,
    toggleActive,
    setDefault,
    workflowsByType
  } = useCustomWorkflow({ productType: selectedProductType });

  // Lấy workflows theo product type hiện tại
  const currentWorkflows = workflowsByType[selectedProductType] || [];

  const handleToggleActive = useCallback(async (workflowId, isActive) => {
    await toggleActive(workflowId, isActive);
  }, [toggleActive]);

  const handleSetDefault = useCallback(async (workflowId) => {
    await setDefault(workflowId, selectedProductType);
  }, [setDefault, selectedProductType]);

  const handleOpenWorkflowBuilder = useCallback(() => {
    window.open('/order-workflows', '_blank');
    onClose();
  }, [onClose]);

  const renderProductTypeSelector = (
    <Box sx={{ mb: 3 }}>
      <Typography variant="subtitle2" sx={{ mb: 2 }}>
        Chọn loại sản phẩm
      </Typography>
      <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
        {Object.entries(PRODUCT_TYPE_LABELS).map(([type, label]) => (
          type !== 'all' && (
            <Chip
              key={type}
              label={label}
              variant={selectedProductType === type ? 'filled' : 'outlined'}
              color={selectedProductType === type ? 'primary' : 'default'}
              onClick={() => setSelectedProductType(type)}
              sx={{ mb: 1 }}
            />
          )
        ))}
      </Stack>
    </Box>
  );

  const renderWorkflowCard = (workflow) => (
    <Card key={workflow.id} variant="outlined" sx={{ p: 2 }}>
      <Stack spacing={2}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography variant="subtitle2" gutterBottom>
              {workflow.name}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {workflow.stages?.length || 0} bước trong quy trình
            </Typography>
          </Box>
          
          <Stack direction="row" spacing={1}>
            {workflow.is_default && (
              <Label color="success" size="small">Mặc định</Label>
            )}
            {workflow.is_active ? (
              <Label color="success" size="small">Hoạt động</Label>
            ) : (
              <Label color="error" size="small">Tạm dừng</Label>
            )}
          </Stack>
        </Stack>
        
        {workflow.description && (
          <Typography variant="caption" color="text.secondary">
            {workflow.description}
          </Typography>
        )}
        
        <Divider />
        
        <Stack direction="row" spacing={1} justifyContent="flex-end">
          {!workflow.is_default && (
            <Button
              size="small"
              variant="outlined"
              onClick={() => handleSetDefault(workflow.id)}
              disabled={loading}
            >
              Đặt làm mặc định
            </Button>
          )}
          
          <Button
            size="small"
            variant="outlined"
            color={workflow.is_active ? 'error' : 'success'}
            onClick={() => handleToggleActive(workflow.id, !workflow.is_active)}
            disabled={loading}
          >
            {workflow.is_active ? 'Tạm dừng' : 'Kích hoạt'}
          </Button>
        </Stack>
      </Stack>
    </Card>
  );

  const renderWorkflowList = (
    <Box>
      <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
        <Typography variant="subtitle2">
          Danh sách quy trình
          <Chip 
            label={currentWorkflows.length} 
            size="small" 
            sx={{ ml: 1 }} 
          />
        </Typography>
        
        <Button
          size="small"
          variant="contained"
          startIcon={<Iconify icon="solar:add-circle-bold" />}
          onClick={handleOpenWorkflowBuilder}
        >
          Tạo mới
        </Button>
      </Stack>
      
      <Grid container spacing={2}>
        {currentWorkflows.map((workflow) => (
          <Grid item size={{xs: 12}} key={workflow.id}>
            {renderWorkflowCard(workflow)}
          </Grid>
        ))}
        
        {currentWorkflows.length === 0 && (
          <Grid item size={{xs: 12}}>
            <Box
              display="flex"
              flexDirection="column"
              alignItems="center"
              justifyContent="center"
              py={4}
              color="text.secondary"
            >
              <Iconify icon="solar:document-text-bold" width={48} sx={{ mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Chưa có quy trình nào
              </Typography>
              <Typography variant="body2" textAlign="center" sx={{ mb: 2 }}>
                Chưa có quy trình nào cho {PRODUCT_TYPE_LABELS[selectedProductType]}
              </Typography>
              <Button
                variant="contained"
                startIcon={<Iconify icon="solar:add-circle-bold" />}
                onClick={handleOpenWorkflowBuilder}
              >
                Tạo quy trình đầu tiên
              </Button>
            </Box>
          </Grid>
        )}
      </Grid>
    </Box>
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={1}>
          <Iconify icon="solar:settings-bold" />
          <Typography variant="h6">
            Cài đặt quy trình đơn hàng
          </Typography>
        </Stack>
      </DialogTitle>
      
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {renderProductTypeSelector}
          {renderWorkflowList}
        </Stack>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Đóng</Button>
        <Button
          variant="contained"
          startIcon={<Iconify icon="solar:settings-bold" />}
          onClick={handleOpenWorkflowBuilder}
        >
          Mở trình quản lý nâng cao
        </Button>
      </DialogActions>
    </Dialog>
  );
}
