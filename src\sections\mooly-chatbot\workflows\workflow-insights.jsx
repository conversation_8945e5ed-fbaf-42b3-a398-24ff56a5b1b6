'use client';

import { useMemo } from 'react';

import {
  <PERSON>,
  Card,
  Stack,
  Typography,
  Grid,
  Chip,
  LinearProgress,
  Tooltip
} from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { Label } from 'src/components/label';

import { PRODUCT_TYPE_LABELS } from 'src/actions/mooly-chatbot/order-status-business-rules';

// ----------------------------------------------------------------------

export default function WorkflowInsights({ workflows = [], selectedProductType = 'all' }) {
  const insights = useMemo(() => {
    const filteredWorkflows = selectedProductType === 'all' 
      ? workflows 
      : workflows.filter(w => w.product_type === selectedProductType);

    const totalWorkflows = filteredWorkflows.length;
    const activeWorkflows = filteredWorkflows.filter(w => w.is_active).length;
    const defaultWorkflows = filteredWorkflows.filter(w => w.is_default).length;
    
    // Stage statistics
    const totalStages = filteredWorkflows.reduce((sum, w) => sum + (w.stages?.length || 0), 0);
    const avgStagesPerWorkflow = totalWorkflows > 0 ? Math.round(totalStages / totalWorkflows) : 0;
    
    // Complexity analysis
    const complexWorkflows = filteredWorkflows.filter(w => {
      const stageCount = w.stages?.length || 0;
      const hasAdvancedFeatures = w.stages?.some(s => 
        s.auto_transition || s.requires_payment || s.requires_inventory
      );
      return stageCount > 5 || hasAdvancedFeatures;
    }).length;

    // Product type distribution
    const productTypeDistribution = workflows.reduce((acc, workflow) => {
      acc[workflow.product_type] = (acc[workflow.product_type] || 0) + 1;
      return acc;
    }, {});

    return {
      totalWorkflows,
      activeWorkflows,
      defaultWorkflows,
      totalStages,
      avgStagesPerWorkflow,
      complexWorkflows,
      productTypeDistribution,
      activePercentage: totalWorkflows > 0 ? Math.round((activeWorkflows / totalWorkflows) * 100) : 0,
      complexityPercentage: totalWorkflows > 0 ? Math.round((complexWorkflows / totalWorkflows) * 100) : 0
    };
  }, [workflows, selectedProductType]);

  const renderStatCard = (title, value, icon, color = 'primary', subtitle = null) => (
    <Card sx={{ p: 2, textAlign: 'center' }}>
      <Stack spacing={1} alignItems="center">
        <Box
          sx={{
            width: 48,
            height: 48,
            borderRadius: '50%',
            bgcolor: `${color}.lighter`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          <Iconify icon={icon} width={24} sx={{ color: `${color}.main` }} />
        </Box>
        
        <Typography variant="h4" color={`${color}.main`}>
          {value}
        </Typography>
        
        <Typography variant="body2" color="text.secondary" textAlign="center">
          {title}
        </Typography>
        
        {subtitle && (
          <Typography variant="caption" color="text.secondary">
            {subtitle}
          </Typography>
        )}
      </Stack>
    </Card>
  );

  const renderProgressCard = (title, value, percentage, color = 'primary') => (
    <Card sx={{ p: 2 }}>
      <Stack spacing={2}>
        <Stack direction="row" justifyContent="space-between" alignItems="center">
          <Typography variant="subtitle2">{title}</Typography>
          <Typography variant="h6" color={`${color}.main`}>
            {value}
          </Typography>
        </Stack>
        
        <Box>
          <LinearProgress
            variant="determinate"
            value={percentage}
            color={color}
            sx={{ height: 8, borderRadius: 4 }}
          />
          <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
            {percentage}% của tổng số
          </Typography>
        </Box>
      </Stack>
    </Card>
  );

  if (workflows.length === 0) {
    return (
      <Card sx={{ p: 3, textAlign: 'center' }}>
        <Iconify icon="solar:chart-2-bold" width={48} sx={{ color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          Chưa có dữ liệu thống kê
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Tạo workflow đầu tiên để xem thống kê chi tiết
        </Typography>
      </Card>
    );
  }

  return (
    <Box>
      <Typography variant="h6" sx={{ mb: 3 }}>
        Thống kê quy trình
        {selectedProductType !== 'all' && (
          <Chip 
            label={PRODUCT_TYPE_LABELS[selectedProductType]} 
            size="small" 
            sx={{ ml: 1 }} 
          />
        )}
      </Typography>

      <Grid container spacing={3}>
        {/* Main Statistics */}
        <Grid item size={{xs: 6, md: 3}}>
          {renderStatCard(
            'Tổng quy trình',
            insights.totalWorkflows,
            'solar:document-text-bold',
            'primary'
          )}
        </Grid>

        <Grid item size={{xs: 6, md: 3}}>
          {renderStatCard(
            'Đang hoạt động',
            insights.activeWorkflows,
            'solar:play-circle-bold',
            'success'
          )}
        </Grid>

        <Grid item size={{xs: 6, md: 3}}>
          {renderStatCard(
            'Tổng bước',
            insights.totalStages,
            'solar:list-check-bold',
            'info'
          )}
        </Grid>

        <Grid item size={{xs: 6, md: 3}}>
          {renderStatCard(
            'TB bước/quy trình',
            insights.avgStagesPerWorkflow,
            'solar:calculator-bold',
            'warning'
          )}
        </Grid>

        {/* Progress Cards */}
        <Grid item size={{xs: 12, md: 6}}>
          {renderProgressCard(
            'Quy trình đang hoạt động',
            insights.activeWorkflows,
            insights.activePercentage,
            'success'
          )}
        </Grid>

        <Grid item size={{xs: 12, md: 6}}>
          {renderProgressCard(
            'Quy trình phức tạp',
            insights.complexWorkflows,
            insights.complexityPercentage,
            'warning'
          )}
        </Grid>

        {/* Product Type Distribution */}
        {selectedProductType === 'all' && Object.keys(insights.productTypeDistribution).length > 1 && (
          <Grid item size={{xs: 12}}>
            <Card sx={{ p: 3 }}>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Phân bố theo loại sản phẩm
              </Typography>
              
              <Grid container spacing={2}>
                {Object.entries(insights.productTypeDistribution).map(([type, count]) => (
                  <Grid item size={{xs: 6, md: 3}} key={type}>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <Label color="primary" size="small">
                        {PRODUCT_TYPE_LABELS[type] || type}
                      </Label>
                      <Typography variant="body2" fontWeight="medium">
                        {count}
                      </Typography>
                    </Stack>
                  </Grid>
                ))}
              </Grid>
            </Card>
          </Grid>
        )}

        {/* Quick Actions */}
        <Grid item size={{xs: 12}}>
          <Card sx={{ p: 3 }}>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Gợi ý tối ưu
            </Typography>
            
            <Stack spacing={1}>
              {insights.defaultWorkflows === 0 && (
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify icon="solar:info-circle-bold" sx={{ color: 'warning.main' }} />
                  <Typography variant="body2">
                    Chưa có quy trình mặc định. Hãy đặt một quy trình làm mặc định.
                  </Typography>
                </Stack>
              )}
              
              {insights.activePercentage < 50 && insights.totalWorkflows > 1 && (
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify icon="solar:play-circle-bold" sx={{ color: 'info.main' }} />
                  <Typography variant="body2">
                    Nhiều quy trình chưa được kích hoạt. Xem xét kích hoạt hoặc xóa bỏ.
                  </Typography>
                </Stack>
              )}
              
              {insights.avgStagesPerWorkflow > 8 && (
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify icon="solar:settings-bold" sx={{ color: 'secondary.main' }} />
                  <Typography variant="body2">
                    Quy trình có nhiều bước. Xem xét đơn giản hóa để tăng hiệu quả.
                  </Typography>
                </Stack>
              )}
              
              {insights.totalWorkflows === 0 && (
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Iconify icon="solar:add-circle-bold" sx={{ color: 'primary.main' }} />
                  <Typography variant="body2">
                    Bắt đầu bằng cách tạo quy trình đầu tiên từ mẫu có sẵn.
                  </Typography>
                </Stack>
              )}
            </Stack>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}
