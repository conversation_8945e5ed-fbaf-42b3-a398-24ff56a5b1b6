'use client';

import { varAlpha } from 'minimal-shared/utils';
import { useState, useEffect, useCallback, useMemo } from 'react';

import Box from '@mui/material/Box';
import Tab from '@mui/material/Tab';
import Tabs from '@mui/material/Tabs';
import Card from '@mui/material/Card';
import Chip from '@mui/material/Chip';
import Stack from '@mui/material/Stack';
import Table from '@mui/material/Table';
import Button from '@mui/material/Button';
import Tooltip from '@mui/material/Tooltip';
import Container from '@mui/material/Container';
import TableBody from '@mui/material/TableBody';
import Typography from '@mui/material/Typography';
import IconButton from '@mui/material/IconButton';
import TableContainer from '@mui/material/TableContainer';

import { paths } from 'src/routes/paths';
import { useRouter } from 'src/routes/hooks';

import { useBoolean } from 'src/hooks/use-boolean';

import { DashboardContent } from 'src/layouts/dashboard';
import { useOrders } from 'src/actions/mooly-chatbot/order-service';
import { ORDER_STATUS_OPTIONS } from 'src/actions/mooly-chatbot/order-constants';

import { Label } from 'src/components/label';
import { toast } from 'src/components/snackbar';
import { Iconify } from 'src/components/iconify';
import { Scrollbar } from 'src/components/scrollbar';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';
import {
  useTable,
  emptyRows,
  rowInPage,
  TableNoData,
  getComparator,
  TableEmptyRows,
  TableHeadCustom,
  TableSelectedAction,
  TablePaginationCustom,
} from 'src/components/table';

import { OrderTableToolbar } from '../order-table-toolbar';
import { OrderCreateDialog } from '../order-create-dialog';
import { EnhancedOrderTableRow } from '../enhanced-order-table-row';
import { EnhancedOrderAnalytics } from '../enhanced-order-analytics';
import { EnhancedBatchOperations } from '../enhanced-batch-operations';
import { OrderTableFiltersResult } from '../order-table-filters-result';
import WorkflowSettingsDialog from '../workflow-settings-dialog';

// ==========================================
// 📊 ORDER LIST VIEW - OPTIMIZED
// ==========================================

const STATUS_OPTIONS = [{ value: 'all', label: 'Tất cả' }, ...ORDER_STATUS_OPTIONS];

const TABLE_HEAD = [
  { id: 'orderNumber', label: 'Mã đơn hàng', width: 120 },
  { id: 'customer', label: 'Khách hàng', minWidth: 200 },
  { id: 'createdAt', label: 'Ngày tạo', width: 140 },
  { id: 'totalAmount', label: 'Tổng tiền', width: 140, align: 'center' },
  { id: 'status', label: 'Trạng thái', width: 110, align: 'center' },
  { id: '', width: 120, align: 'right' }, // Increase width for action column
];

const defaultFilters = {
  name: '',
  status: 'all',
  startDate: null,
  endDate: null,
};

export default function OrderListView() {
  const router = useRouter();

  const table = useTable({ defaultOrderBy: 'createdAt', defaultOrder: 'desc' });
  const createDialog = useBoolean();
  const analyticsView = useBoolean();
  const workflowDialog = useBoolean();

  const [filters, setFilters] = useState(defaultFilters);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Memoize order options để tránh infinite re-renders
  const orderOptions = useMemo(() => {
    const filterOptions = {};

    // Chỉ thêm filter khi có giá trị
    if (filters.status !== 'all') {
      filterOptions.status = filters.status;
    } else {
      // Khi chọn "Tất cả", loại trừ đơn hàng bị hủy và hoàn thành
      filterOptions.status = {
        notIn: ['cancelled', 'completed']
      };
    }

    if (filters.name) {
      filterOptions.or = [
        { orderNumber: { contains: filters.name, mode: 'insensitive' } },
        { customerName: { contains: filters.name, mode: 'insensitive' } },
        { customerEmail: { contains: filters.name, mode: 'insensitive' } },
        { customerPhone: { contains: filters.name, mode: 'insensitive' } },
      ];
    }

    if (filters.startDate && filters.endDate) {
      filterOptions.createdAt = {
        gte: filters.startDate,
        lte: filters.endDate,
      };
    }

    return {
      filters: filterOptions,
      orderBy: table.orderBy,
      ascending: table.order === 'asc',
      limit: table.rowsPerPage,
      offset: table.page * table.rowsPerPage,
      count: true,
    };
  }, [
    filters.status,
    filters.name,
    filters.startDate,
    filters.endDate,
    table.orderBy,
    table.order,
    table.rowsPerPage,
    table.page
  ]);

  // Options để lấy tất cả đơn hàng cho việc tính toán chỉ số tabs
  const allOrdersOptions = useMemo(() => {
    const filterOptions = {};

    // Chỉ áp dụng filter name và date
    if (filters.name) {
      filterOptions.or = [
        { orderNumber: { contains: filters.name, mode: 'insensitive' } },
        { customerName: { contains: filters.name, mode: 'insensitive' } },
        { customerEmail: { contains: filters.name, mode: 'insensitive' } },
        { customerPhone: { contains: filters.name, mode: 'insensitive' } },
      ];
    }

    if (filters.startDate && filters.endDate) {
      filterOptions.createdAt = {
        gte: filters.startDate,
        lte: filters.endDate,
      };
    }

    return {
      filters: filterOptions,
      orderBy: 'createdAt',
      ascending: false, // Mới nhất trước
      count: true,
    };
  }, [
    filters.name,
    filters.startDate,
    filters.endDate
  ]);

  const { orders = [], isLoading, mutate: refetch } = useOrders(orderOptions);
  const { orders: allOrders = [], mutate: refetchAll } = useOrders(allOrdersOptions);

  // Trigger refetch khi refreshTrigger thay đổi
  useEffect(() => {
    if (refreshTrigger > 0) {
      refetch();
      refetchAll(); // Cũng cần refetch allOrders để cập nhật chỉ số tabs
    }
  }, [refreshTrigger, refetch, refetchAll]);

  // Memoize filtered data để tránh re-calculate mỗi render
  const dataFiltered = useMemo(() =>
    applyFilter({
      inputData: orders,
      comparator: getComparator(table.order, table.orderBy),
      filters,
    }), [orders, table.order, table.orderBy, filters.status, filters.name, filters.startDate, filters.endDate]
  );

  const dataInPage = useMemo(() =>
    rowInPage(dataFiltered, table.page, table.rowsPerPage),
    [dataFiltered, table.page, table.rowsPerPage]
  );

  const canReset = useMemo(() =>
    Object.keys(filters).some((key) => filters[key] !== defaultFilters[key]),
    [filters.status, filters.name, filters.startDate, filters.endDate]
  );

  const notFound = (!dataFiltered.length && canReset) || (!dataFiltered.length && !isLoading);



  const handleFilterStatus = useCallback(
    (_, newValue) => {
      table.onResetPage();
      setFilters((prevState) => ({
        ...prevState,
        status: newValue,
      }));
    },
    [table]
  );

  const handleCancelRow = useCallback(
    async (id) => {
      try {
        // Import cancelOrder function
        const { cancelOrder } = await import('src/actions/mooly-chatbot/order-service');

        // Gọi API hủy đơn hàng
        const result = await cancelOrder(id, 'Hủy đơn hàng từ enhanced list');

        if (result.success) {
          // Chỉ trigger refresh một lần
          setRefreshTrigger(prev => prev + 1);
          toast.success('Hủy đơn hàng thành công!');
        } else {
          toast.error(result.error || 'Hủy đơn hàng thất bại!');
        }
      } catch (error) {
        console.error(error);
        toast.error('Hủy đơn hàng thất bại!');
      }
    },
    []
  );

  const handleCancelRows = useCallback(async () => {
    try {
      // Import cancelOrder function
      const { cancelOrder } = await import('src/actions/mooly-chatbot/order-service');

      // Gọi API hủy nhiều đơn hàng
      const results = await Promise.all(
        table.selected.map((id) => cancelOrder(id, 'Hủy hàng loạt từ enhanced list'))
      );

      // Kiểm tra kết quả
      const failedCount = results.filter(result => !result.success).length;
      const successCount = results.length - failedCount;

      // Clear selection và refresh data
      table.onSelectAllRows(false, []);
      setRefreshTrigger(prev => prev + 1);

      if (failedCount === 0) {
        toast.success(`Hủy thành công ${successCount} đơn hàng!`);
      } else {
        toast.warning(`Hủy thành công ${successCount}/${results.length} đơn hàng!`);
      }
    } catch (error) {
      console.error(error);
      toast.error('Hủy đơn hàng thất bại!');
    }
  }, [table]);

  const handleEditRow = useCallback(
    (id) => {
      router.push(paths.dashboard.moolyChatbot.orders.details(id));
    },
    [router]
  );

  const handleViewRow = useCallback(
    (id) => {
      router.push(paths.dashboard.moolyChatbot.orders.details(id));
    },
    [router]
  );

  const handleStatusUpdate = useCallback(() => {
    // Chỉ trigger refresh một lần
    setRefreshTrigger(prev => prev + 1);
  }, []);

  const handleBatchSuccess = useCallback(() => {
    // Clear selection
    table.onSelectAllRows(false, []);

    // Chỉ trigger refresh một lần
    setRefreshTrigger(prev => prev + 1);
  }, [table]);

  const selectedOrders = useMemo(() =>
    orders.filter(order => table.selected.includes(order.id)),
    [orders, table.selected]
  );

  // Memoize tab counts để tránh tính toán lại mỗi render
  const tabCounts = useMemo(() => {
    // Tab "Tất cả" loại trừ đơn hàng bị hủy và hoàn thành
    const allCount = allOrders.filter(order =>
      order.status !== 'cancelled' && order.status !== 'completed'
    ).length;

    const counts = { all: allCount };

    STATUS_OPTIONS.forEach(option => {
      if (option.value !== 'all') {
        counts[option.value] = allOrders.filter(order => order.status === option.value).length;
      }
    });

    return counts;
  }, [allOrders]);

  return (
    <DashboardContent>
      <Container maxWidth="xl">
        <CustomBreadcrumbs
          heading="Quản lý đơn hàng"
          links={[
            { name: 'Dashboard', href: paths.dashboard.root },
            { name: 'Đơn hàng', href: paths.dashboard.moolyChatbot.orders.root },
            { name: 'Danh sách' },
          ]}
          action={
            <Stack
              direction={{ xs: 'column', sm: 'row' }}
              spacing={1}
              sx={{
                width: { xs: '100%', sm: 'auto' },
                '& .MuiButton-root': {
                  minWidth: { xs: '100%', sm: 'auto' },
                  whiteSpace: 'nowrap'
                }
              }}
            >
              <Tooltip title="Xem thống kê">
                <IconButton
                  onClick={analyticsView.onToggle}
                  color={analyticsView.value ? 'primary' : 'default'}
                  sx={{
                    alignSelf: { xs: 'flex-start', sm: 'center' },
                    flexShrink: 0
                  }}
                >
                  <Iconify icon="solar:chart-bold" />
                </IconButton>
              </Tooltip>

              {/* Tạm thời ẩn nút cài đặt quy trình để phát triển sau */}
              {/* <Button
                variant="outlined"
                startIcon={<Iconify icon="solar:settings-bold" />}
                href='/dashboard/mooly-chatbot/orders/order-workflows/'
                sx={{
                  flexShrink: 0,
                  fontSize: { xs: '0.875rem', sm: '0.875rem' }
                }}
              >
                Cài đặt quy trình
              </Button> */}

              <Button
                variant="contained"
                startIcon={<Iconify icon="mingcute:add-line" />}
                onClick={createDialog.onTrue}
                sx={{
                  flexShrink: 0,
                  fontSize: { xs: '0.875rem', sm: '0.875rem' }
                }}
              >
                Tạo đơn hàng
              </Button>
            </Stack>
          }
          sx={{
            mb: { xs: 3, md: 5 },
          }}
        />

        {/* Analytics View */}
        {analyticsView.value && (
          <Card sx={{ mb: 3 }}>
            <EnhancedOrderAnalytics
              dateRange={{
                startDate: filters.startDate,
                endDate: filters.endDate
              }}
              refreshTrigger={refreshTrigger}
            />
          </Card>
        )}

        {/* Batch Operations */}
        <EnhancedBatchOperations
          selectedOrders={selectedOrders}
          onSuccess={handleBatchSuccess}
          onClearSelection={() => table.onSelectAllRows(false, [])}
        />

        <Card>
          {/* Quick Stats - Tối ưu hiển thị */}
          <Box sx={{ p: 2.5, pb: 0 }}>
            <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap" justifyContent="space-between">
              <Stack direction="row" spacing={2} alignItems="center" flexWrap="wrap">
                {table.selected.length > 0 && (
                  <Chip
                    label={`${table.selected.length} đơn hàng được chọn`}
                    color="primary"
                    variant="filled"
                    size="small"
                    icon={<Iconify icon="solar:check-circle-bold" width={16} />}
                  />
                )}
                {filters.status !== 'all' && (
                  <Chip
                    label={`${dataFiltered.length} đơn hàng ${STATUS_OPTIONS.find(s => s.value === filters.status)?.label?.toLowerCase()}`}
                    color="info"
                    variant="outlined"
                    size="small"
                  />
                )}
              </Stack>

              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 500 }}>
                Tổng cộng: <Box component="span" sx={{ color: 'primary.main', fontWeight: 600 }}>{allOrders.length}</Box> đơn hàng
              </Typography>
            </Stack>
          </Box>

          {/* Status Tabs - Tối ưu cho doanh nghiệp */}
          <Tabs
            value={filters.status}
            onChange={handleFilterStatus}
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
            sx={[
              (theme) => ({
                px: 2.5,
                boxShadow: `inset 0 -2px 0 0 ${varAlpha(theme.vars.palette.grey['500Channel'], 0.08)}`,
                '& .MuiTabs-scrollButtons': {
                  '&.Mui-disabled': { opacity: 0.3 },
                },
                '& .MuiTab-root': {
                  minHeight: 48,
                  fontWeight: 600,
                  textTransform: 'none',
                  fontSize: '0.875rem',
                  '&:hover': {
                    color: theme.vars.palette.primary.main,
                    opacity: 0.8,
                    backgroundColor: varAlpha(theme.vars.palette.primary.mainChannel, 0.04),
                  },
                  '&.Mui-selected': {
                    color: theme.vars.palette.primary.main,
                    fontWeight: 700,
                  },
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0',
                },
              }),
            ]}
          >
            {STATUS_OPTIONS.map((tab) => (
              <Tab
                key={tab.value}
                iconPosition="end"
                value={tab.value}
                label={tab.label}
                icon={
                  <Label
                    variant={
                      tab.value === 'all' || tab.value === filters.status ? 'filled' : 'soft'
                    }
                    color={
                      (tab.value === 'completed' && 'success') ||
                      (tab.value === 'processing' && 'info') ||
                      (tab.value === 'pending' && 'warning') ||
                      (tab.value === 'cancelled' && 'error') ||
                      (tab.value === 'refunded' && 'secondary') ||
                      (tab.value === 'confirmed' && 'info') ||
                      (tab.value === 'paid' && 'success') ||
                      (tab.value === 'shipping' && 'primary') ||
                      (tab.value === 'delivered' && 'success') ||
                      'default'
                    }
                    sx={{
                      minWidth: 24,
                      height: 20,
                      fontSize: '0.75rem',
                      fontWeight: 600,
                    }}
                  >
                    {tabCounts[tab.value] || 0}
                  </Label>
                }
              />
            ))}
          </Tabs>

          <OrderTableToolbar
            filters={{
              state: filters,
              setState: setFilters
            }}
            onResetPage={table.onResetPage}
          />

          {canReset && (
            <OrderTableFiltersResult
              filters={{
                state: filters,
                setState: setFilters
              }}
              onResetPage={table.onResetPage}
              totalResults={dataFiltered.length}
              sx={{ p: 2.5, pt: 0 }}
            />
          )}

          <TableContainer sx={{ position: 'relative', overflow: 'unset' }}>
            <TableSelectedAction
              dense={table.dense}
              numSelected={table.selected.length}
              rowCount={dataFiltered.length}
              onSelectAllRows={(checked) =>
                table.onSelectAllRows(
                  checked,
                  dataFiltered.map((row) => row.id)
                )
              }
              action={
                <Tooltip title="Hủy đơn hàng">
                  <IconButton color="error" onClick={handleCancelRows}>
                    <Iconify icon="solar:close-circle-bold" />
                  </IconButton>
                </Tooltip>
              }
            />

            <Scrollbar>
              <Table
                size={table.dense ? 'small' : 'medium'}
                sx={{
                  minWidth: 960,
                  '& .MuiTableCell-root': {
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                  },
                  '& .MuiTableCell-root:last-child': {
                    whiteSpace: 'nowrap',
                    minWidth: 120,
                  }
                }}
              >
                <TableHeadCustom
                  order={table.order}
                  orderBy={table.orderBy}
                  headCells={TABLE_HEAD}
                  rowCount={dataFiltered.length}
                  numSelected={table.selected.length}
                  onSort={table.onSort}
                  onSelectAllRows={(checked) =>
                    table.onSelectAllRows(
                      checked,
                      dataFiltered.map((row) => row.id)
                    )
                  }
                />

                <TableBody>
                  {dataInPage.map((row) => (
                    <EnhancedOrderTableRow
                      key={row.id}
                      row={row}
                      selected={table.selected.includes(row.id)}
                      onSelectRow={() => table.onSelectRow(row.id)}
                      onDeleteRow={() => handleCancelRow(row.id)}
                      onEditRow={() => handleEditRow(row.id)}
                      onViewRow={() => handleViewRow(row.id)}
                      onStatusUpdate={handleStatusUpdate}
                    />
                  ))}

                  <TableEmptyRows
                    height={table.dense ? 52 : 72}
                    emptyRows={emptyRows(table.page, table.rowsPerPage, dataFiltered.length)}
                  />

                  <TableNoData notFound={notFound} />
                </TableBody>
              </Table>
            </Scrollbar>
          </TableContainer>

          <TablePaginationCustom
            count={dataFiltered.length}
            page={table.page}
            rowsPerPage={table.rowsPerPage}
            onPageChange={table.onChangePage}
            onRowsPerPageChange={table.onChangeRowsPerPage}
            dense={table.dense}
            onChangeDense={table.onChangeDense}
          />
        </Card>
      </Container>

      <OrderCreateDialog
        open={createDialog.value}
        onClose={createDialog.onFalse}
        onSuccess={() => {
          createDialog.onFalse();
          setRefreshTrigger(prev => prev + 1);
        }}
      />

      {/* Workflow Settings Dialog */}
      <WorkflowSettingsDialog
        open={workflowDialog.value}
        onClose={workflowDialog.onFalse}
      />
    </DashboardContent>
  );
}

// ==========================================
// 🔍 FILTER FUNCTION
// ==========================================

function applyFilter({ inputData, comparator, filters }) {
  const { name, status, startDate, endDate } = filters;

  const stabilizedThis = inputData.map((el, index) => [el, index]);

  stabilizedThis.sort((a, b) => {
    const order = comparator(a[0], b[0]);
    if (order !== 0) return order;
    return a[1] - b[1];
  });

  inputData = stabilizedThis.map((el) => el[0]);

  if (name) {
    inputData = inputData.filter(
      (order) =>
        order.orderNumber?.toLowerCase().indexOf(name.toLowerCase()) !== -1 ||
        order.customerName?.toLowerCase().indexOf(name.toLowerCase()) !== -1 ||
        order.customerPhone?.toLowerCase().indexOf(name.toLowerCase()) !== -1
    );
  }

  if (status !== 'all') {
    inputData = inputData.filter((order) => order.status === status);
  }

  if (startDate && endDate) {
    inputData = inputData.filter((order) => {
      const orderDate = new Date(order.createdAt);
      return orderDate >= startDate && orderDate <= endDate;
    });
  }

  return inputData;
}
