'use client';

import { useState, useCallback } from 'react';
import useSWR from 'swr';

import { toast } from 'src/components/snackbar';

import { fetchData, createData, updateData, deleteData } from './supabase-utils';
import { ORDER_STATUS_OPTIONS, getOrderStatusInfo } from './order-constants';

/**
 * =====================================================
 * CUSTOM WORKFLOW SERVICE
 * =====================================================
 * 
 * Quản lý workflow tùy chỉnh cho đơn hàng:
 * - Tạo, cập nhật, xóa workflow
 * - Quản lý stages và transitions
 * - Validation business rules
 * - Integration với order management
 */

// Table names
const WORKFLOWS_TABLE = 'order_workflows';
const STAGES_TABLE = 'workflow_stages';
const TRANSITIONS_TABLE = 'order_workflow_transitions';
const INSTANCES_TABLE = 'order_workflow_instances';
const HISTORY_TABLE = 'order_workflow_history';

// Default workflow templates
export const DEFAULT_WORKFLOW_TEMPLATES = {
  simple: {
    name: 'Quy trình sản phẩm đơn giản',
    description: 'Quy trình chuẩn cho sản phẩm vật lý đơn giản',
    product_type: 'simple',
    stages: [
      { name: 'Chờ xác nhận', status_code: 'pending', color: 'warning', sort_order: 1, is_start_stage: true },
      { name: 'Đã xác nhận', status_code: 'confirmed', color: 'info', sort_order: 2 },
      { name: 'Đã thanh toán', status_code: 'paid', color: 'success', sort_order: 3, requires_payment: true },
      { name: 'Đang đóng gói', status_code: 'packaging', color: 'info', sort_order: 4, requires_inventory: true },
      { name: 'Đang vận chuyển', status_code: 'shipping', color: 'primary', sort_order: 5 },
      { name: 'Đã giao hàng', status_code: 'delivered', color: 'success', sort_order: 6 },
      { name: 'Hoàn thành', status_code: 'completed', color: 'success', sort_order: 7, is_end_stage: true }
    ]
  },
  digital: {
    name: 'Quy trình sản phẩm số',
    description: 'Quy trình cho sản phẩm số và download',
    product_type: 'digital',
    stages: [
      { name: 'Chờ xác nhận', status_code: 'pending', color: 'warning', sort_order: 1, is_start_stage: true },
      { name: 'Đã xác nhận', status_code: 'confirmed', color: 'info', sort_order: 2 },
      { name: 'Đã thanh toán', status_code: 'paid', color: 'success', sort_order: 3, requires_payment: true },
      { name: 'Đang chuẩn bị', status_code: 'preparing', color: 'info', sort_order: 4 },
      { name: 'Sẵn sàng tải xuống', status_code: 'ready_download', color: 'success', sort_order: 5 },
      { name: 'Đã gửi', status_code: 'sent', color: 'success', sort_order: 6 },
      { name: 'Hoàn thành', status_code: 'completed', color: 'success', sort_order: 7, is_end_stage: true }
    ]
  },
  service: {
    name: 'Quy trình dịch vụ',
    description: 'Quy trình cho các dịch vụ và booking',
    product_type: 'service',
    stages: [
      { name: 'Chờ xác nhận', status_code: 'pending', color: 'warning', sort_order: 1, is_start_stage: true },
      { name: 'Đã xác nhận', status_code: 'confirmed', color: 'info', sort_order: 2 },
      { name: 'Đã thanh toán', status_code: 'paid', color: 'success', sort_order: 3, requires_payment: true },
      { name: 'Đang lên lịch', status_code: 'scheduling', color: 'warning', sort_order: 4 },
      { name: 'Đang thực hiện', status_code: 'in_progress', color: 'primary', sort_order: 5 },
      { name: 'Đã cung cấp', status_code: 'provided', color: 'success', sort_order: 6 },
      { name: 'Hoàn thành', status_code: 'completed', color: 'success', sort_order: 7, is_end_stage: true }
    ]
  }
};

/**
 * Lấy danh sách workflows
 * @param {Object} options - Tùy chọn filter
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getWorkflows(options = {}) {
  const { productType, isActive = true } = options;
  
  const filters = { is_active: isActive };
  if (productType && productType !== 'all') {
    filters.product_type = productType;
  }

  return fetchData(WORKFLOWS_TABLE, {
    filters,
    orderBy: 'created_at',
    ascending: false,
    select: `
      *,
      stages:workflow_stages(
        *,
        transitions_from:order_workflow_transitions!from_stage_id(*)
      )
    `
  });
}

/**
 * Lấy chi tiết workflow
 * @param {string} workflowId - ID workflow
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getWorkflowDetail(workflowId) {
  if (!workflowId) {
    return { success: false, error: 'Thiếu ID workflow', data: null };
  }

  return fetchData(WORKFLOWS_TABLE, {
    filters: { id: workflowId },
    select: `
      *,
      stages:workflow_stages(
        *,
        transitions_from:order_workflow_transitions!from_stage_id(*),
        transitions_to:order_workflow_transitions!to_stage_id(*)
      )
    `
  });
}

/**
 * Validate workflow data trước khi tạo
 * @param {Object} workflowData - Dữ liệu workflow
 * @returns {Object} - Validation result
 */
function validateWorkflowData(workflowData) {
  const errors = [];

  // Required fields
  if (!workflowData.name?.trim()) {
    errors.push('Tên workflow không được để trống');
  }

  if (!workflowData.product_type) {
    errors.push('Loại sản phẩm không được để trống');
  }

  // Validate stages if provided
  if (workflowData.stages && Array.isArray(workflowData.stages)) {
    const { stages } = workflowData;

    if (stages.length === 0) {
      errors.push('Workflow phải có ít nhất một stage');
    }

    // Check for duplicate status codes
    const statusCodes = stages.map(s => s.status_code).filter(Boolean);
    const duplicates = statusCodes.filter((code, index) => statusCodes.indexOf(code) !== index);
    if (duplicates.length > 0) {
      errors.push(`Trạng thái bị trùng lặp: ${duplicates.join(', ')}`);
    }

    // Check start/end stages
    const startStages = stages.filter(s => s.is_start_stage);
    const endStages = stages.filter(s => s.is_end_stage);

    if (startStages.length === 0) {
      errors.push('Workflow phải có ít nhất một stage bắt đầu');
    }

    if (startStages.length > 1) {
      errors.push('Workflow chỉ có thể có một stage bắt đầu');
    }

    if (endStages.length === 0) {
      errors.push('Workflow phải có ít nhất một stage kết thúc');
    }

    // Validate each stage
    stages.forEach((stage, index) => {
      if (!stage.name?.trim()) {
        errors.push(`Stage ${index + 1}: Tên stage không được để trống`);
      }

      if (!stage.status_code) {
        errors.push(`Stage ${index + 1}: Status code không được để trống`);
      }

      // Validate status code exists
      const statusInfo = getOrderStatusInfo(stage.status_code);
      if (!statusInfo) {
        errors.push(`Stage ${index + 1}: Status code "${stage.status_code}" không hợp lệ`);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Tạo workflow mới với comprehensive validation và error handling
 * @param {Object} workflowData - Dữ liệu workflow
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createWorkflow(workflowData) {
  try {
    // 1. Validate input data
    const validation = validateWorkflowData(workflowData);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errors.join('; '),
        code: 'VALIDATION_ERROR'
      };
    }

    const { stages = [], ...workflowInfo } = workflowData;

    // 2. Check for duplicate workflow name trong cùng product type
    const existingWorkflows = await fetchData(WORKFLOWS_TABLE, {
      filters: {
        name: workflowInfo.name.trim(),
        product_type: workflowInfo.product_type
      }
    });

    if (existingWorkflows.success && existingWorkflows.data.length > 0) {
      return {
        success: false,
        error: `Workflow "${workflowInfo.name}" đã tồn tại cho loại sản phẩm này`,
        code: 'DUPLICATE_NAME'
      };
    }

    // 3. Tạo workflow với transaction-like behavior
    const workflowResult = await createData(WORKFLOWS_TABLE, {
      ...workflowInfo,
      name: workflowInfo.name.trim()
    });

    if (!workflowResult.success) {
      return {
        success: false,
        error: workflowResult.error?.message || 'Không thể tạo workflow',
        code: 'CREATE_WORKFLOW_FAILED'
      };
    }

    const workflow = workflowResult.data[0];

    // 4. Tạo stages nếu có
    let createdStages = [];
    if (stages.length > 0) {
      try {
        const stagesData = stages.map((stage, index) => ({
          ...stage,
          workflow_id: workflow.id,
          sort_order: stage.sort_order || index + 1,
          name: stage.name.trim()
        }));

        const stagesResult = await createData(STAGES_TABLE, stagesData);
        if (!stagesResult.success) {
          // Rollback workflow
          await deleteData(WORKFLOWS_TABLE, { id: workflow.id });
          return {
            success: false,
            error: stagesResult.error?.message || 'Không thể tạo stages',
            code: 'CREATE_STAGES_FAILED'
          };
        }

        createdStages = stagesResult.data.sort((a, b) => a.sort_order - b.sort_order);

        // 5. Tạo transitions tự động
        const transitions = [];
        for (let i = 0; i < createdStages.length - 1; i++) {
          const fromStage = createdStages[i];
          const toStage = createdStages[i + 1];

          transitions.push({
            workflow_id: workflow.id,
            from_stage_id: fromStage.id,
            to_stage_id: toStage.id,
            name: `${fromStage.name} → ${toStage.name}`,
            description: `Chuyển từ ${fromStage.name} sang ${toStage.name}`,
            is_automatic: fromStage.auto_transition || false,
            requires_confirmation: !fromStage.auto_transition
          });
        }

        if (transitions.length > 0) {
          const transitionsResult = await createData(TRANSITIONS_TABLE, transitions);
          if (!transitionsResult.success) {
            console.warn('Không thể tạo transitions tự động:', transitionsResult.error);
            // Không rollback vì transitions không critical
          }
        }

      } catch (stageError) {
        // Rollback workflow nếu có lỗi khi tạo stages
        await deleteData(WORKFLOWS_TABLE, { id: workflow.id });
        throw stageError;
      }
    }

    return {
      success: true,
      data: {
        ...workflow,
        stages: createdStages
      },
      message: `Đã tạo workflow "${workflow.name}" thành công với ${createdStages.length} bước`
    };

  } catch (error) {
    console.error('Error in createWorkflow:', error);
    return {
      success: false,
      error: error.message || 'Lỗi không xác định khi tạo workflow',
      code: 'UNKNOWN_ERROR'
    };
  }
}

/**
 * Cập nhật workflow
 * @param {string} workflowId - ID workflow
 * @param {Object} workflowData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateWorkflow(workflowId, workflowData) {
  if (!workflowId) {
    return { success: false, error: 'Thiếu ID workflow' };
  }

  try {
    const result = await updateData(WORKFLOWS_TABLE, workflowData, { id: workflowId });
    
    if (result.success) {
      return {
        success: true,
        data: result.data[0],
        message: 'Đã cập nhật workflow thành công'
      };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật workflow'
    };
  }
}

/**
 * Xóa workflow
 * @param {string} workflowId - ID workflow
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteWorkflow(workflowId) {
  if (!workflowId) {
    return { success: false, error: 'Thiếu ID workflow' };
  }

  try {
    // Kiểm tra xem có đơn hàng nào đang sử dụng workflow này không
    const instancesResult = await fetchData(INSTANCES_TABLE, {
      filters: { workflow_id: workflowId },
      select: 'id'
    });

    if (instancesResult.success && instancesResult.data.length > 0) {
      return {
        success: false,
        error: 'Không thể xóa workflow đang được sử dụng bởi các đơn hàng'
      };
    }

    const result = await deleteData(WORKFLOWS_TABLE, { id: workflowId });
    
    if (result.success) {
      return {
        success: true,
        message: 'Đã xóa workflow thành công'
      };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi xóa workflow'
    };
  }
}

/**
 * Tạo workflow từ template với advanced customization
 * @param {string} templateType - Loại template (simple, digital, service)
 * @param {Object} customData - Dữ liệu tùy chỉnh
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createWorkflowFromTemplate(templateType, customData = {}) {
  try {
    const template = DEFAULT_WORKFLOW_TEMPLATES[templateType];
    if (!template) {
      return {
        success: false,
        error: `Template "${templateType}" không tồn tại`,
        code: 'TEMPLATE_NOT_FOUND'
      };
    }

    // Validate template compatibility với product type
    if (customData.product_type && customData.product_type !== template.product_type) {
      return {
        success: false,
        error: `Template "${templateType}" không tương thích với loại sản phẩm "${customData.product_type}"`,
        code: 'TEMPLATE_INCOMPATIBLE'
      };
    }

    // Generate unique name nếu không có custom name
    let finalName = customData.name || template.name;
    if (!customData.name) {
      const timestamp = new Date().toLocaleString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
      finalName = `${template.name} - ${timestamp}`;
    }

    // Merge template với custom data
    const workflowData = {
      ...template,
      ...customData,
      name: finalName,
      description: customData.description || template.description,
      // Ensure product_type consistency
      product_type: customData.product_type || template.product_type,
      // Apply custom settings
      is_active: customData.is_active !== undefined ? customData.is_active : true,
      is_default: customData.is_default !== undefined ? customData.is_default : false
    };

    // Customize stages nếu cần
    if (template.stages && Array.isArray(template.stages)) {
      workflowData.stages = template.stages.map((stage, index) => ({
        ...stage,
        // Apply any stage-level customizations
        sort_order: stage.sort_order || index + 1,
        // Ensure proper defaults
        notify_customer: stage.notify_customer !== undefined ? stage.notify_customer : true,
        notify_admin: stage.notify_admin !== undefined ? stage.notify_admin : false,
        auto_transition: stage.auto_transition !== undefined ? stage.auto_transition : false,
        requires_payment: stage.requires_payment !== undefined ? stage.requires_payment : false,
        requires_inventory: stage.requires_inventory !== undefined ? stage.requires_inventory : false
      }));
    }

    // Create workflow using optimized createWorkflow function
    const result = await createWorkflow(workflowData);

    if (result.success) {
      return {
        ...result,
        message: `Đã tạo workflow từ template "${templateType}" thành công`,
        templateUsed: templateType
      };
    }

    return result;

  } catch (error) {
    console.error('Error in createWorkflowFromTemplate:', error);
    return {
      success: false,
      error: error.message || 'Lỗi không xác định khi tạo workflow từ template',
      code: 'UNKNOWN_ERROR'
    };
  }
}

// =====================================================
// STAGE MANAGEMENT FUNCTIONS
// =====================================================

/**
 * Tạo stage mới trong workflow
 * @param {string} workflowId - ID workflow
 * @param {Object} stageData - Dữ liệu stage
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createWorkflowStage(workflowId, stageData) {
  if (!workflowId) {
    return { success: false, error: 'Thiếu ID workflow' };
  }

  try {
    // Validate status_code
    const statusInfo = getOrderStatusInfo(stageData.status_code);
    if (!statusInfo) {
      return {
        success: false,
        error: `Trạng thái "${stageData.status_code}" không hợp lệ`
      };
    }

    const stageWithWorkflow = {
      ...stageData,
      workflow_id: workflowId,
      name: stageData.name || statusInfo.label,
      color: stageData.color || statusInfo.color,
      hex_color: stageData.hex_color || statusInfo.hexColor
    };

    const result = await createData(STAGES_TABLE, stageWithWorkflow);

    if (result.success) {
      return {
        success: true,
        data: result.data[0],
        message: `Đã tạo stage "${stageWithWorkflow.name}" thành công`
      };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi tạo stage'
    };
  }
}

/**
 * Cập nhật stage
 * @param {string} stageId - ID stage
 * @param {Object} stageData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateWorkflowStage(stageId, stageData) {
  if (!stageId) {
    return { success: false, error: 'Thiếu ID stage' };
  }

  try {
    const result = await updateData(STAGES_TABLE, stageData, { id: stageId });

    if (result.success) {
      return {
        success: true,
        data: result.data[0],
        message: 'Đã cập nhật stage thành công'
      };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật stage'
    };
  }
}

/**
 * Xóa stage
 * @param {string} stageId - ID stage
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteWorkflowStage(stageId) {
  if (!stageId) {
    return { success: false, error: 'Thiếu ID stage' };
  }

  try {
    // Kiểm tra xem có transitions nào liên quan không
    const transitionsResult = await fetchData(TRANSITIONS_TABLE, {
      filters: {
        or: `from_stage_id.eq.${stageId},to_stage_id.eq.${stageId}`
      },
      select: 'id'
    });

    if (transitionsResult.success && transitionsResult.data.length > 0) {
      return {
        success: false,
        error: 'Không thể xóa stage đang có transitions liên quan'
      };
    }

    const result = await deleteData(STAGES_TABLE, { id: stageId });

    if (result.success) {
      return {
        success: true,
        message: 'Đã xóa stage thành công'
      };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi xóa stage'
    };
  }
}
